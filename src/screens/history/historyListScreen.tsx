import React, {useState, useCallback} from 'react';
import {
  StyleSheet,
  FlatList,
  SafeAreaView,
  View,
  Text,
  ActivityIndicator,
  TouchableOpacity,
  Linking,
} from 'react-native';
import CustomSearchBar from '../../components/CustomSearchBar';
import historyRepository, {
  HistoryData,
} from '../../database/watermelon/repositories/historyRepository';
import {useFocusEffect} from '@react-navigation/native';
import {NativeAd} from 'react-native-google-mobile-ads';
import {debounce} from 'lodash';
import CustomNativeAdCard from '../../components/ads/CustomNativeAdCard';
import adMobService from '../../services/adMobService';
import commonStyles from '../../common/commonStyles';

const HistoryListScreen = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [originalHistoryItems, setOriginalHistoryItems] = useState<
    HistoryData[]
  >([]);
  const [filteredItems, setFilteredItems] = useState<HistoryData[]>([]);

  // Native ad state
  const [nativeAd, setNativeAd] = useState<NativeAd | null>(null);

  // Load history data
  const loadHistoryData = async () => {
    try {
      setLoading(true);
      const history = await historyRepository.getNumberTapHistory();
      console.log('Number tap history items count:', history.length);

      // Store the original list for search filtering
      setOriginalHistoryItems(history);

      // Apply search filter if query exists
      if (searchQuery && searchQuery.trim() !== '') {
        const filtered = history.filter(item => {
          return (
            item.company_name
              ?.toLowerCase()
              .includes(searchQuery.toLowerCase()) ||
            false ||
            item.number?.toLowerCase().includes(searchQuery.toLowerCase()) ||
            false
          );
        });
        setFilteredItems(filtered);
      } else {
        setFilteredItems(history);
      }
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      setLoading(false);
    }
  };

  // Use useFocusEffect to reload data when the screen comes into focus
  useFocusEffect(
    useCallback(() => {
      adMobService.initiateNativeAd().then(ad => setNativeAd(ad));

      console.log('History screen focused - reloading data');
      loadHistoryData();

      // Return a cleanup function (optional)
      return () => {
        // This runs when the screen is unfocused
        console.log('History screen unfocused');
      };
    }, []),
  );

  // Filter search data function
  const filterSearchedData = (searchText: string) => {
    console.log('Filtering history with search text:', searchText);

    if (!searchText || searchText.trim() === '') {
      // Restore the original list if search text is empty
      console.log('Restoring original history list');
      setFilteredItems([...originalHistoryItems]);
      return;
    }

    // Filter history items based on the search text (case-insensitive)
    const filteredHistory = originalHistoryItems.filter(item => {
      return (
        item.company_name?.toLowerCase().includes(searchText.toLowerCase()) ||
        false ||
        item.number?.toLowerCase().includes(searchText.toLowerCase()) ||
        false
      );
    });

    setFilteredItems(filteredHistory);
  };

  const handlePhoneCall = async (number: string) => {
    const phoneNumber = `tel:${number}`;
    Linking.openURL(phoneNumber).catch((err: Error) =>
      console.error('Failed to open phone call:', err),
    );
  };

  const debouncedSearch = useCallback(
    debounce((text: string) => {
      console.log('Debounced search text:', text);
      filterSearchedData(text);
    }, 300),
    [originalHistoryItems],
  );

  const handleSearchChange = (text: string) => {
    setSearchQuery(text);
    debouncedSearch(text);
  };

  return (
    <SafeAreaView style={styles.container}>
      <CustomSearchBar
        onSearch={handleSearchChange}
        onVoiceResult={result => {
          setSearchQuery(result);
          handleSearchChange(result);
        }}
        initialValue={searchQuery}
        placeholder="Search history"
        showVoiceSearch={true}
      />

      {loading ? (
        <View style={styles.loaderContainer}>
          <ActivityIndicator size="large" color="#0000ff" />
          <Text style={styles.loaderText}>Loading history...</Text>
        </View>
      ) : filteredItems.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No history found</Text>
        </View>
      ) : (
        <FlatList
          style={{padding: 15}}
          contentContainerStyle={{paddingBottom: nativeAd ? 230 : 55}}
          data={filteredItems}
          keyExtractor={item => item.id?.toString() || ''}
          renderItem={({item}) => {
            // Check if this is a number tap history entry (has company_name and number)
            return (
              <View style={styles.numberTapCard}>
                <Text style={styles.companyNameText}>{item.company_name}</Text>
                <TouchableOpacity
                  onPress={() => handlePhoneCall(item.number ?? '')}>
                  <Text style={styles.numberText}>{item.number}</Text>
                </TouchableOpacity>
                <Text style={styles.timestampText}>
                  {item.viewed_at
                    ? new Date(item.viewed_at).toLocaleString()
                    : ''}
                </Text>
              </View>
            );
          }}
          showsVerticalScrollIndicator={false}
          showsHorizontalScrollIndicator={false}
        />
      )}
      {/* Native Ad - Sticky at bottom */}
      {nativeAd && (
        <View style={commonStyles.nativeAdContainer}>
          <CustomNativeAdCard ad={nativeAd} />
        </View>
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loaderText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
    fontFamily: 'Poppins-Regular',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    fontFamily: 'Poppins-Medium',
    textAlign: 'center',
  },
  numberTapCard: {
    flexDirection: 'column',
    alignItems: 'stretch',
    backgroundColor: '#f1f5f9',
    borderRadius: 10,
    marginBottom: 10,
    borderWidth: 1.5,
    padding: 15,
    borderColor: '#D7E2F1',
    marginHorizontal: 5,
    justifyContent: 'space-evenly',
  },
  companyNameText: {
    fontSize: 16,
    fontFamily: 'Poppins-Bold',
    color: '#333',
    marginBottom: 5,
  },
  numberText: {
    fontSize: 18,
    fontFamily: 'Poppins-Medium',
    color: '#0066cc',
    textDecorationLine: 'underline',
    marginBottom: 5,
  },
  timestampText: {
    fontSize: 12,
    fontFamily: 'Poppins-Regular',
    color: '#666',
  },
});

export default HistoryListScreen;
